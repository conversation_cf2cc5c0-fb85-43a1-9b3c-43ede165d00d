package com.nercar.contract.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @description: 流程处理状态信息
 * @author: <PERSON> AI
 * @date: 2025/08/05
 */
@Data
public class ProcessStatusInfo {
    
    private String currentStatus;        // 当前状态：DISTRIBUTED(已分发) / RETURNED(已退回)
    private LocalDateTime processTime;   // 处理时间
    
    // 分发信息（当状态为DISTRIBUTED时有值）
    private DistributionInfo distribution;
    
    // 退回信息（当状态为RETURNED时有值）  
    private ReturnInfo returnInfo;
}
